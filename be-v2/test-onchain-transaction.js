/**
 * 🔍 链上交易数据分析
 * 
 * 直接从链上获取交易数据，对比 VaultTransaction 存储的 message 和实际的链上交易
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { connection } = require('./utils');

/**
 * 获取链上交易的详细信息
 */
async function getOnchainTransactionDetails(signature) {
  console.log(`\n🔍 获取链上交易: ${signature}`);
  
  try {
    // 获取交易详情
    const transaction = await connection.getTransaction(signature, {
      maxSupportedTransactionVersion: 0,
      commitment: 'confirmed'
    });

    if (!transaction) {
      console.log('❌ 交易不存在或未确认');
      return null;
    }

    console.log(`✅ 交易状态: ${transaction.meta?.err ? '失败' : '成功'}`);
    console.log(`📊 交易费用: ${transaction.meta?.fee} lamports`);

    const message = transaction.transaction.message;
    console.log(`\n📋 链上交易消息分析:`);
    console.log(`   账户数量: ${message.staticAccountKeys.length}`);
    console.log(`   指令数量: ${message.compiledInstructions.length}`);

    // 显示所有账户
    console.log(`\n📝 链上账户列表:`);
    message.staticAccountKeys.forEach((key, index) => {
      const keyStr = key.toBase58();
      let description = '';
      
      if (keyStr === '11111111111111111111111111111111') {
        description = ' (SystemProgram)';
      } else if (keyStr === 'ComputeBudget111111111111111111111111111111') {
        description = ' (Compute Budget)';
      } else if (keyStr === 'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf') {
        description = ' (Squads Program V4)';
      }
      
      console.log(`   [${index}] ${keyStr}${description}`);
    });

    // 分析每个指令
    console.log(`\n🔧 链上指令分析:`);
    message.compiledInstructions.forEach((instruction, index) => {
      console.log(`\n--- 指令 #${index + 1} ---`);
      console.log(`程序ID索引: ${instruction.programIdIndex}`);
      
      if (instruction.programIdIndex < message.staticAccountKeys.length) {
        const programId = message.staticAccountKeys[instruction.programIdIndex];
        console.log(`程序ID: ${programId.toBase58()}`);
        
        // 识别程序类型
        const programIdStr = programId.toBase58();
        if (programIdStr === '11111111111111111111111111111111') {
          console.log(`程序类型: SystemProgram`);
          
          // 解析 SystemProgram 指令
          if (instruction.data && instruction.data.length >= 4) {
            const instructionType = instruction.data.readUInt32LE(0);
            console.log(`指令类型: ${instructionType} ${instructionType === 2 ? '(Transfer)' : ''}`);
            
            if (instructionType === 2 && instruction.data.length >= 12) {
              const amount = instruction.data.readBigUInt64LE(4);
              console.log(`转账金额: ${amount} lamports (${Number(amount) / 1e9} SOL)`);
              
              if (instruction.accountKeyIndexes && instruction.accountKeyIndexes.length >= 2) {
                const fromIndex = instruction.accountKeyIndexes[0];
                const toIndex = instruction.accountKeyIndexes[1];
                console.log(`从地址: ${message.staticAccountKeys[fromIndex]?.toBase58()}`);
                console.log(`到地址: ${message.staticAccountKeys[toIndex]?.toBase58()}`);
              }
            }
          }
        } else if (programIdStr === 'ComputeBudget111111111111111111111111111111') {
          console.log(`程序类型: Compute Budget`);
        } else if (programIdStr === 'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf') {
          console.log(`程序类型: Squads Program V4`);
        }
      }
      
      console.log(`账户索引: [${instruction.accountKeyIndexes.join(', ')}]`);
      console.log(`数据长度: ${instruction.data.length} bytes`);
    });

    return {
      transaction,
      message,
      analysis: {
        accountCount: message.staticAccountKeys.length,
        instructionCount: message.compiledInstructions.length,
        systemInstructions: message.compiledInstructions.filter(inst => 
          message.staticAccountKeys[inst.programIdIndex]?.toBase58() === '11111111111111111111111111111111'
        )
      }
    };

  } catch (error) {
    console.error(`❌ 获取链上交易失败:`, error.message);
    return null;
  }
}

/**
 * 对比链上交易和 VaultTransaction 存储的差异
 */
async function compareOnchainVsVaultTransaction() {
  console.log('🚀 开始对比链上交易和 VaultTransaction 存储的差异...\n');

  // 交易签名（从 Solscan 获取）
  const transactions = [
    {
      index: 43,
      signature: '5UmgFNghVL4RLFwPPi5MEoUUAyxmYTrq7YhWRgAmLfkUe9AHLEiWCwX7Wkq79DPL1qkkV2aSRug5KsVPehiah8MT',
      description: '官方平台创建'
    },
    {
      index: 44,
      signature: '3xi8qjMoSDyh7uhkQYXxSHuVa1n6F3k5NLKobvr5o7AtKBh2GN9jS5EcE3P5LQsfk6Rmo2bEorBiaqk5zFKcTMfc',
      description: '自建平台创建'
    }
  ];

  for (const tx of transactions) {
    console.log(`${'='.repeat(80)}`);
    console.log(`📊 分析交易 #${tx.index} (${tx.description})`);
    console.log(`${'='.repeat(80)}`);

    // 1. 获取链上交易数据
    const onchainData = await getOnchainTransactionDetails(tx.signature);
    
    if (!onchainData) {
      console.log('❌ 无法获取链上数据，跳过');
      continue;
    }

    // 2. 获取 VaultTransaction 数据（从之前的分析中我们知道结构）
    console.log(`\n📋 VaultTransaction vs 链上交易对比:`);
    
    if (tx.index === 43) {
      console.log(`   VaultTransaction 账户数: 2`);
      console.log(`   链上交易账户数: ${onchainData.analysis.accountCount}`);
      console.log(`   VaultTransaction 指令数: 1`);
      console.log(`   链上交易指令数: ${onchainData.analysis.instructionCount}`);
      console.log(`   链上 SystemProgram 指令: ${onchainData.analysis.systemInstructions.length} 个`);
      
      console.log(`\n💡 问题分析:`);
      console.log(`   ❌ VaultTransaction 只存储了转账相关的账户 (from, to)`);
      console.log(`   ❌ VaultTransaction 没有存储 SystemProgram 账户引用`);
      console.log(`   ❌ 但指令的 programIdIndex 仍然指向 SystemProgram 的位置`);
      console.log(`   🔍 这导致 programIdIndex 超出 VaultTransaction.message.accountKeys 范围`);
      
    } else if (tx.index === 44) {
      console.log(`   VaultTransaction 账户数: 3`);
      console.log(`   链上交易账户数: ${onchainData.analysis.accountCount}`);
      console.log(`   VaultTransaction 指令数: 1`);
      console.log(`   链上交易指令数: ${onchainData.analysis.instructionCount}`);
      console.log(`   链上 SystemProgram 指令: ${onchainData.analysis.systemInstructions.length} 个`);
      
      console.log(`\n✅ 正常情况:`);
      console.log(`   ✅ VaultTransaction 正确存储了所有相关账户`);
      console.log(`   ✅ 包含了 SystemProgram 账户引用`);
      console.log(`   ✅ programIdIndex 指向正确的位置`);
    }

    // 等待一下避免 RPC 限流
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

/**
 * 提出修复方案
 */
function proposeFixSolution() {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`🔧 修复方案`);
  console.log(`${'='.repeat(80)}`);

  console.log(`\n🎯 问题根源:`);
  console.log(`   官方 Squads 平台在创建 VaultTransaction 时，`);
  console.log(`   只在 message.accountKeys 中存储了转账相关的账户，`);
  console.log(`   但没有包含 SystemProgram 的账户引用。`);
  console.log(`   然而指令的 programIdIndex 仍然指向 SystemProgram 应该在的位置。`);

  console.log(`\n💡 修复策略:`);
  console.log(`   1. 检测 programIdIndex 超出范围的情况`);
  console.log(`   2. 根据指令数据推断程序类型`);
  console.log(`   3. 如果指令数据符合 SystemProgram.transfer 格式，直接解析`);

  console.log(`\n🔧 具体实现:`);
  console.log(`   - 在解析器中添加容错逻辑`);
  console.log(`   - 当 programIdIndex 超出范围时，检查指令数据`);
  console.log(`   - 如果数据格式匹配 transfer 指令，直接解析`);
  console.log(`   - 使用 accountIndexes 获取转账的 from/to 地址`);
}

/**
 * 主测试函数
 */
async function runOnchainAnalysis() {
  try {
    await compareOnchainVsVaultTransaction();
    proposeFixSolution();
    
    console.log(`\n🎉 分析完成！`);
    console.log(`现在我们知道了问题的根源，可以实施修复方案。`);
    
  } catch (error) {
    console.error('❌ 分析过程中出现错误:', error);
  }
}

// 运行分析
if (require.main === module) {
  runOnchainAnalysis().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('分析失败:', error);
    process.exit(1);
  });
}

module.exports = {
  getOnchainTransactionDetails,
  compareOnchainVsVaultTransaction,
  proposeFixSolution,
  runOnchainAnalysis
};
