const {
  parseTransactionDetails,
  parseMultipleTransactions,
  enhanceTransactionList
} = require('./transaction-parser');
const multisig = require('@sqds/multisig');
const { PublicKey } = require('@solana/web3.js');
const { connection, MULTISIG_PROGRAM_ID } = require('./utils');

/**
 * 测试增强的交易解析器
 */
async function testEnhancedParser() {
  console.log('=== 增强交易解析器测试 ===\n');

  // 🔧 配置测试参数
  // 请替换为你的实际多签地址
  const TEST_MULTISIG_ADDRESS = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr';

  if (TEST_MULTISIG_ADDRESS === 'YOUR_MULTISIG_ADDRESS_HERE') {
    console.log('❌ 请先在脚本中设置实际的多签地址');
    console.log('   修改 TEST_MULTISIG_ADDRESS 变量');
    return;
  }

  try {
    // 1. 获取多签基本信息
    console.log('1️⃣ 获取多签基本信息...');
    const multisigPubkey = new PublicKey(TEST_MULTISIG_ADDRESS);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    console.log(`   多签地址: ${TEST_MULTISIG_ADDRESS}`);
    console.log(`   当前交易索引: ${multisigData.transactionIndex}`);
    console.log(`   成员数量: ${multisigData.members.length}`);
    console.log(`   投票阈值: ${multisigData.threshold}`);

    // 2. 确定要测试的交易索引
    const currentIndex = Number(multisigData.transactionIndex.toString());
    const testIndexes = [];

    // 测试最近的 5 个交易（如果存在）
    for (let i = Math.max(1, currentIndex - 4); i <= currentIndex; i++) {
      testIndexes.push(i);
    }

    console.log(`\n2️⃣ 将测试交易索引: [${testIndexes.join(', ')}]`);

    // 3. 测试单个交易解析
    console.log('\n3️⃣ 测试单个交易解析...');
    for (const index of testIndexes) {
      console.log(`\n--- 解析交易 #${index} ---`);

      try {
        const result = await parseTransactionDetails(TEST_MULTISIG_ADDRESS, index);

        if (result) {
          console.log(`✅ 解析成功:`);
          console.log(`   类型: ${result.type}`);
          console.log(`   金额: ${result.amount} ${result.tokenSymbol || ''}`);
          console.log(`   从地址: ${result.fromAddress || 'N/A'}`);
          console.log(`   到地址: ${result.toAddress || 'N/A'}`);
          console.log(`   创建者: ${result.creator}`);
          console.log(`   创建时间: ${result.createdAt || 'N/A'}`);

          if (result.type === 'token') {
            console.log(`   Token Mint: ${result.tokenMint}`);
            console.log(`   精度: ${result.decimals}`);
          }
        } else {
          console.log(`⚠️  无法解析（可能不是转账交易或账户不存在）`);
        }
      } catch (error) {
        console.log(`❌ 解析失败: ${error.message}`);
      }
    }

    // 4. 测试批量解析
    console.log('\n4️⃣ 测试批量解析...');
    try {
      const batchResults = await parseMultipleTransactions(TEST_MULTISIG_ADDRESS, testIndexes);
      console.log(`✅ 批量解析完成，成功解析 ${batchResults.length} 个交易:`);

      batchResults.forEach(result => {
        console.log(`   #${result.transactionIndex}: ${result.type} - ${result.amount} ${result.tokenSymbol || ''} -> ${result.toAddress?.substring(0, 8)}...`);
      });
    } catch (error) {
      console.log(`❌ 批量解析失败: ${error.message}`);
    }

    // 5. 测试与现有 API 的集成
    console.log('\n5️⃣ 测试与现有 API 的集成...');
    try {
      // 模拟现有 API 返回的交易列表格式
      const mockTransactions = testIndexes.map(index => ({
        transactionIndex: index,
        status: 'Active',
        approvals: 1,
        threshold: multisigData.threshold,
        creator: 'Unknown',
        memo: null,
        votes: [],
        canExecute: false,
        transactionType: 'unknown',
        transferAmount: null,
        transferToken: null,
        toAddress: null,
        createdAt: new Date().toISOString()
      }));

      console.log(`   原始交易列表: ${mockTransactions.length} 个交易`);

      const enhancedTransactions = await enhanceTransactionList(mockTransactions, TEST_MULTISIG_ADDRESS);

      console.log(`   增强后交易列表:`);
      enhancedTransactions.forEach(tx => {
        if (tx.isParsed) {
          console.log(`   ✅ #${tx.transactionIndex}: ${tx.transactionType} - ${tx.transferAmount} ${tx.transferToken} -> ${tx.toAddress?.substring(0, 8)}... (${tx.parseSource})`);
        } else {
          console.log(`   ⚠️  #${tx.transactionIndex}: 未解析 (${tx.parseSource})`);
        }
      });

      // 统计解析成功率
      const parsedCount = enhancedTransactions.filter(tx => tx.isParsed).length;
      const successRate = (parsedCount / enhancedTransactions.length * 100).toFixed(1);
      console.log(`   解析成功率: ${parsedCount}/${enhancedTransactions.length} (${successRate}%)`);

    } catch (error) {
      console.log(`❌ 集成测试失败: ${error.message}`);
    }

    // 6. 性能测试
    console.log('\n6️⃣ 性能测试...');
    try {
      const startTime = Date.now();
      await parseMultipleTransactions(TEST_MULTISIG_ADDRESS, testIndexes);
      const endTime = Date.now();

      const avgTime = (endTime - startTime) / testIndexes.length;
      console.log(`   解析 ${testIndexes.length} 个交易耗时: ${endTime - startTime}ms`);
      console.log(`   平均每个交易: ${avgTime.toFixed(1)}ms`);
    } catch (error) {
      console.log(`❌ 性能测试失败: ${error.message}`);
    }

    console.log('\n🎉 测试完成！');
    console.log('\n📋 总结:');
    console.log('   ✅ 可以从 VaultTransaction 中解析出完整的转账信息');
    console.log('   ✅ 支持 SOL 和 SPL Token 转账');
    console.log('   ✅ 可以获取准确的转账金额、目标地址、创建时间');
    console.log('   ✅ 可以无缝集成到现有的 API 中');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

/**
 * 演示如何在实际 API 中使用
 */
function demonstrateApiUsage() {
  console.log('\n=== API 集成示例 ===');
  console.log(`
// 在 router.js 中的使用示例:
const { enhanceTransactionList } = require('./transaction-parser');

router.post('/api/transactions', async (ctx) => {
  try {
    const { multisigAddress } = ctx.request.body;

    // ... 现有的获取交易逻辑 ...

    // 🎯 关键改进：使用增强解析器
    const enhancedTransactions = await enhanceTransactionList(transactions, multisigAddress);

    ctx.body = { transactions: enhancedTransactions };
  } catch (error) {
    handleError(ctx, error, '获取交易列表失败');
  }
});

// 现在前端可以获得完整的转账信息：
// - transferAmount: 准确的转账金额
// - toAddress: 准确的目标地址
// - createdAt: 准确的创建时间
// - transferToken: Token 符号
// - isParsed: 是否成功解析
  `);
}

// 运行测试
if (require.main === module) {
  testEnhancedParser()
    .then(() => {
      demonstrateApiUsage();
      process.exit(0);
    })
    .catch(error => {
      console.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = {
  testEnhancedParser,
  demonstrateApiUsage
};
