/**
 * 🎯 前后端集成测试
 * 
 * 测试更新后的前后端代码，验证新增的字段是否正确传递和显示
 */

const { enhanceTransactionList } = require('./transaction-parser');

/**
 * 模拟后端 API 响应
 */
async function simulateBackendAPI() {
  console.log('=== 🔧 后端 API 模拟测试 ===\n');

  const multisigAddress = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr';
  
  // 模拟原始交易数据（从数据库或链上获取的基础信息）
  const originalTransactions = [
    {
      transactionIndex: 44,
      status: 'Executed',
      approvals: 2,
      threshold: 2,
      creator: 'EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG',
      memo: null,
      votes: [
        { member: 'EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG', vote: 'Approve' },
        { member: 'ABC123...', vote: 'Approve' }
      ],
      canExecute: false,
      createdAt: '2024-01-01T00:00:00Z', // 旧的估算时间
      // 原来只有这些基础字段
      transactionType: 'unknown',
      transferAmount: null,
      transferToken: null,
      toAddress: null,
      fromAddress: null,
      tokenMint: null
    },
    {
      transactionIndex: 45,
      status: 'Approved',
      approvals: 1,
      threshold: 2,
      creator: 'EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG',
      memo: null,
      votes: [
        { member: 'EMXYq4vz4QEyxj63gerfCGjxGBrBFc7gfoiNdpf85RiG', vote: 'Approve' }
      ],
      canExecute: true,
      createdAt: '2024-01-01T01:00:00Z', // 旧的估算时间
      // 原来只有这些基础字段
      transactionType: 'unknown',
      transferAmount: null,
      transferToken: null,
      toAddress: null,
      fromAddress: null,
      tokenMint: null
    }
  ];

  console.log('📋 原始交易数据（解析前）:');
  originalTransactions.forEach(tx => {
    console.log(`   #${tx.transactionIndex}: ${tx.transactionType} - ${tx.transferAmount} - ${tx.createdAt}`);
  });

  // 🎯 使用增强解析器
  console.log('\n🔧 正在使用增强解析器...');
  const startTime = Date.now();
  
  const enhancedTransactions = await enhanceTransactionList(originalTransactions, multisigAddress);
  
  const endTime = Date.now();
  const parsedCount = enhancedTransactions.filter(tx => tx.isParsed).length;

  console.log(`✅ 解析完成! 耗时: ${endTime - startTime}ms`);
  console.log(`📊 解析统计: ${parsedCount}/${enhancedTransactions.length} 个交易成功解析\n`);

  // 模拟后端 API 响应格式
  const apiResponse = {
    transactions: enhancedTransactions,
    summary: {
      total: enhancedTransactions.length,
      parsed: parsedCount,
      parseRate: `${(parsedCount / enhancedTransactions.length * 100).toFixed(1)}%`,
      parseTime: `${endTime - startTime}ms`
    }
  };

  console.log('📤 后端 API 响应格式:');
  console.log('   - transactions: Array(' + apiResponse.transactions.length + ')');
  console.log('   - summary:', apiResponse.summary);

  return apiResponse;
}

/**
 * 模拟前端处理
 */
function simulateFrontendProcessing(apiResponse) {
  console.log('\n=== 🖥️ 前端处理模拟测试 ===\n');

  const { transactions } = apiResponse;

  console.log('📥 前端接收到的数据:');
  transactions.forEach(tx => {
    console.log(`\n交易 #${tx.transactionIndex}:`);
    console.log(`   - 类型: ${tx.transactionType} ${tx.isParsed ? '✅' : '❌'}`);
    console.log(`   - 金额: ${tx.transferAmount} ${tx.transferToken || ''}`);
    console.log(`   - 从地址: ${tx.fromAddress ? tx.fromAddress.substring(0, 8) + '...' : '-'}`);
    console.log(`   - 到地址: ${tx.toAddress ? tx.toAddress.substring(0, 8) + '...' : '-'}`);
    console.log(`   - 创建时间: ${tx.createdAt}`);
    console.log(`   - 状态: ${tx.status}`);
    console.log(`   - 投票: ${tx.approvals}/${tx.threshold}`);
  });

  // 模拟前端表格渲染逻辑
  console.log('\n🎨 前端表格渲染预览:');
  console.log('┌─────┬──────┬──────────┬──────────┬──────────┬─────────────────────┬─────────┬────────┐');
  console.log('│ ID  │ 类型 │   金额   │  从地址  │  到地址  │      创建时间       │  状态   │ 投票   │');
  console.log('├─────┼──────┼──────────┼──────────┼──────────┼─────────────────────┼─────────┼────────┤');
  
  transactions.forEach(tx => {
    const id = `#${tx.transactionIndex}`.padEnd(3);
    const type = tx.isParsed && tx.transactionType !== 'unknown' 
      ? tx.transactionType.toUpperCase().padEnd(4) 
      : '未知'.padEnd(4);
    const amount = tx.transferAmount 
      ? `${tx.transferAmount} ${tx.transferToken}`.padEnd(8)
      : '-'.padEnd(8);
    const fromAddr = tx.fromAddress ? (tx.fromAddress.substring(0, 4) + '...').padEnd(8) : '-'.padEnd(8);
    const toAddr = tx.toAddress ? (tx.toAddress.substring(0, 4) + '...').padEnd(8) : '-'.padEnd(8);
    const time = new Date(tx.createdAt).toLocaleString('zh-CN').substring(5, 16).padEnd(19);
    const status = tx.status.padEnd(7);
    const votes = `${tx.approvals}/${tx.threshold}`.padEnd(6);
    
    console.log(`│ ${id} │ ${type} │ ${amount} │ ${fromAddr} │ ${toAddr} │ ${time} │ ${status} │ ${votes} │`);
  });
  
  console.log('└─────┴──────┴──────────┴──────────┴──────────┴─────────────────────┴─────────┴────────┘');
}

/**
 * 验证数据完整性
 */
function validateDataIntegrity(apiResponse) {
  console.log('\n=== ✅ 数据完整性验证 ===\n');

  const { transactions } = apiResponse;
  let passedTests = 0;
  let totalTests = 0;

  // 测试1: 所有交易都有基础字段
  totalTests++;
  const hasBasicFields = transactions.every(tx => 
    tx.transactionIndex !== undefined &&
    tx.status !== undefined &&
    tx.creator !== undefined &&
    tx.createdAt !== undefined
  );
  if (hasBasicFields) {
    console.log('✅ 测试1: 所有交易都包含基础字段');
    passedTests++;
  } else {
    console.log('❌ 测试1: 缺少基础字段');
  }

  // 测试2: 解析成功的交易有完整转账信息
  totalTests++;
  const parsedTransactions = transactions.filter(tx => tx.isParsed);
  const hasCompleteTransferInfo = parsedTransactions.every(tx =>
    tx.transactionType !== 'unknown' &&
    tx.transferAmount !== null &&
    tx.transferToken !== null &&
    tx.fromAddress !== null &&
    tx.toAddress !== null
  );
  if (hasCompleteTransferInfo) {
    console.log('✅ 测试2: 解析成功的交易包含完整转账信息');
    passedTests++;
  } else {
    console.log('❌ 测试2: 解析成功的交易缺少转账信息');
  }

  // 测试3: 创建时间已更新为准确时间
  totalTests++;
  const hasAccurateTime = parsedTransactions.every(tx => 
    tx.createdAt && !tx.createdAt.startsWith('2024-01-01')
  );
  if (hasAccurateTime) {
    console.log('✅ 测试3: 创建时间已更新为准确时间');
    passedTests++;
  } else {
    console.log('❌ 测试3: 创建时间仍为估算时间');
  }

  // 测试4: 解析状态字段正确
  totalTests++;
  const hasParseStatus = transactions.every(tx =>
    tx.isParsed !== undefined &&
    (tx.isParsed ? tx.parseSource === 'vaultTransaction' : tx.parseSource === 'memo')
  );
  if (hasParseStatus) {
    console.log('✅ 测试4: 解析状态字段正确');
    passedTests++;
  } else {
    console.log('❌ 测试4: 解析状态字段错误');
  }

  console.log(`\n📊 验证结果: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！前后端集成成功！');
  } else {
    console.log('⚠️  部分测试失败，需要检查代码');
  }

  return passedTests === totalTests;
}

/**
 * 主测试函数
 */
async function runIntegrationTest() {
  console.log('🚀 开始前后端集成测试...\n');

  try {
    // 1. 模拟后端 API
    const apiResponse = await simulateBackendAPI();

    // 2. 模拟前端处理
    simulateFrontendProcessing(apiResponse);

    // 3. 验证数据完整性
    const isValid = validateDataIntegrity(apiResponse);

    console.log('\n' + '='.repeat(60));
    if (isValid) {
      console.log('🎉 集成测试完成！新功能已成功实现：');
      console.log('   ✅ 准确的转账金额显示');
      console.log('   ✅ 完整的从地址和到地址');
      console.log('   ✅ 精确的创建时间');
      console.log('   ✅ 转账类型识别');
      console.log('   ✅ 解析状态标识');
      console.log('\n现在你的 Squads 平台可以显示与官方平台相同的详细信息！');
    } else {
      console.log('❌ 集成测试失败，请检查代码实现');
    }

  } catch (error) {
    console.error('❌ 集成测试出错:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  runIntegrationTest();
}

module.exports = {
  simulateBackendAPI,
  simulateFrontendProcessing,
  validateDataIntegrity,
  runIntegrationTest
};
