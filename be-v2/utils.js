const { Connection, <PERSON>Key, SystemProgram } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');
const {
  MULTISIG_PROGRAM_ID,
  SOLANA_RPC_URL,
  SOLANA_COMMITMENT,
  TX_TIMEOUT,
  TX_MAX_RETRIES,
  MAX_PROPOSALS_TO_CHECK,
  FIXED_MULTISIG_ADDRESS
} = require('./constants');

// 创建 Solana 连接
const connection = new Connection(SOLANA_RPC_URL, SOLANA_COMMITMENT);

// 多签地址管理函数
const getCurrentMultisigAddress = () => {
  return FIXED_MULTISIG_ADDRESS;
};

// 检查是否有配置的多签地址
const hasConfiguredMultisigAddress = () => {
  return FIXED_MULTISIG_ADDRESS !== null && FIXED_MULTISIG_ADDRESS !== '';
};

// 工具函数
const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key]);
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null;
};

const handleError = (ctx, error, message) => {
  const status = error.status || 500;
  ctx.status = status;
  ctx.body = { error: message || error.message };
};

const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: SOLANA_COMMITMENT,
      maxRetries: TX_MAX_RETRIES,
      ...options
    }
  );
  return signature;
};

const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, SOLANA_COMMITMENT),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ]);
    return { confirmed: true, result };
  } catch (error) {
    return { confirmed: false, error: error.message };
  }
};

// ==================== 交易解析功能 ====================

/**
 * 解析指令数据格式（统一处理对象或Buffer）
 */
function parseInstructionData(instruction) {
  if (!instruction.data) return null;

  if (Buffer.isBuffer(instruction.data)) {
    return instruction.data;
  } else if (typeof instruction.data === 'object') {
    return Buffer.from(Object.values(instruction.data));
  }

  return null;
}

/**
 * 解析账户索引（统一处理数组或对象）
 */
function parseAccountIndexes(instruction) {
  if (instruction.accountKeyIndexes) {
    return instruction.accountKeyIndexes;
  } else if (instruction.accountIndexes) {
    return Array.isArray(instruction.accountIndexes)
      ? instruction.accountIndexes
      : Object.values(instruction.accountIndexes);
  }
  return [];
}

/**
 * 解析 SystemProgram.transfer 指令
 */
async function parseSystemTransfer(instruction, message) {
  try {
    const dataBuffer = parseInstructionData(instruction);
    if (!dataBuffer || dataBuffer.length < 12) return null;

    const instructionType = dataBuffer.readUInt32LE(0);
    if (instructionType !== 2) return null; // 不是 transfer 指令

    const amount = dataBuffer.readBigUInt64LE(4);
    const accountIndexes = parseAccountIndexes(instruction);

    if (accountIndexes.length < 2) return null;

    const fromIndex = accountIndexes[0];
    const toIndex = accountIndexes[1];
    const fromAddress = message.accountKeys[fromIndex].toBase58();
    const toAddress = message.accountKeys[toIndex].toBase58();

    return {
      type: 'sol',
      amount: Number(amount) / 1e9,
      fromAddress,
      toAddress,
      tokenSymbol: 'SOL',
      decimals: 9
    };
  } catch (error) {
    return null;
  }
}

/**
 * 兼容性解析：处理官方 Squads 平台创建的交易
 * 当 programIdIndex 超出范围时使用
 */
async function parseSystemTransferFallback(instruction, message) {
  try {
    const dataBuffer = parseInstructionData(instruction);
    if (!dataBuffer || dataBuffer.length < 12) return null;

    const instructionType = dataBuffer.readUInt32LE(0);
    if (instructionType !== 2) return null;

    const amount = dataBuffer.readBigUInt64LE(4);
    const accountIndexes = parseAccountIndexes(instruction);

    if (accountIndexes.length < 2) return null;

    const fromIndex = accountIndexes[0];
    const toIndex = accountIndexes[1];

    // 确保账户索引在范围内
    if (fromIndex >= message.accountKeys.length || toIndex >= message.accountKeys.length) {
      return null;
    }

    const fromAddress = message.accountKeys[fromIndex].toBase58();
    const toAddress = message.accountKeys[toIndex].toBase58();

    return {
      type: 'sol',
      amount: Number(amount) / 1e9,
      fromAddress,
      toAddress,
      tokenSymbol: 'SOL',
      decimals: 9
    };
  } catch (error) {
    return null;
  }
}

/**
 * 解析单个交易的详细信息
 */
async function parseTransactionDetails(multisigAddress, transactionIndex) {
  try {
    const multisigPubkey = new PublicKey(multisigAddress);

    // 获取 Proposal 数据
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (!proposalAccount) return null;

    const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];

    // 获取 VaultTransaction 数据
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      index: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    const transactionAccount = await connection.getAccountInfo(transactionPda);
    if (!transactionAccount) return null;

    const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
    const message = vaultTransactionData.message;

    // 初始化转账信息
    const transferInfo = {
      type: 'unknown',
      amount: null,
      toAddress: null,
      fromAddress: null,
      tokenMint: null,
      tokenSymbol: null,
      decimals: null,
      createdAt: null,
      creator: proposalData.creator?.toBase58() || 'Unknown'
    };

    // 解析每个指令
    for (const instruction of message.instructions) {
      // 检查 programIdIndex 是否超出范围（官方平台兼容性处理）
      if (instruction.programIdIndex >= message.accountKeys.length) {
        const solTransfer = await parseSystemTransferFallback(instruction, message);
        if (solTransfer) {
          Object.assign(transferInfo, solTransfer);
          break;
        }
        continue;
      }

      const programId = message.accountKeys[instruction.programIdIndex];

      // 解析 SOL 转账
      if (programId.equals(SystemProgram.programId)) {
        const solTransfer = await parseSystemTransfer(instruction, message);
        if (solTransfer) {
          Object.assign(transferInfo, solTransfer);
          break;
        }
      }
      // 可以在这里添加 SPL Token 解析，但目前项目中未使用
    }

    // 获取创建时间
    if (proposalData.status && proposalData.status.timestamp) {
      const timestamp = Number(proposalData.status.timestamp.toString());
      transferInfo.createdAt = new Date(timestamp * 1000).toISOString();
    }

    return transferInfo;

  } catch (error) {
    console.error(`解析交易 ${transactionIndex} 失败:`, error.message);
    return null;
  }
}

/**
 * 增强现有的交易列表，添加解析后的转账信息
 */
async function enhanceTransactionList(transactions, multisigAddress) {
  const enhanced = [];

  for (const tx of transactions) {
    const parsedDetails = await parseTransactionDetails(multisigAddress, tx.transactionIndex);

    if (parsedDetails && parsedDetails.type !== 'unknown') {
      // 使用解析出的详细信息
      enhanced.push({
        ...tx,
        transactionType: parsedDetails.type,
        transferAmount: parsedDetails.amount,
        transferToken: parsedDetails.tokenSymbol,
        toAddress: parsedDetails.toAddress,
        fromAddress: parsedDetails.fromAddress,
        tokenMint: parsedDetails.tokenMint,
        createdAt: parsedDetails.createdAt || tx.createdAt,
        creator: parsedDetails.creator || tx.creator,
        isParsed: true,
        parseSource: 'vaultTransaction'
      });
    } else {
      // 保持原有信息，标记为未解析
      enhanced.push({
        ...tx,
        isParsed: false,
        parseSource: 'memo'
      });
    }
  }

  return enhanced;
}

module.exports = {
  MULTISIG_PROGRAM_ID,
  connection,
  TX_TIMEOUT,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError,
  sendTransaction,
  confirmTransactionWithTimeout,
  getCurrentMultisigAddress,
  hasConfiguredMultisigAddress,
  // 交易解析功能
  enhanceTransactionList,
  parseTransactionDetails
};