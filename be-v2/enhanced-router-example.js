/**
 * 增强版路由示例 - 展示如何集成交易解析器
 * 这个文件展示了如何将新的解析器集成到现有的 router.js 中
 */

const Router = require('koa-router');
const multisig = require('@sqds/multisig');
const { PublicKey } = require('@solana/web3.js');
const { enhanceTransactionList } = require('./transaction-parser');
const {
  MULTISIG_PROGRAM_ID,
  connection,
  MAX_PROPOSALS_TO_CHECK,
  validateParams,
  handleError
} = require('./utils');

const router = new Router();

/**
 * 🎯 增强版获取交易列表 API
 * 这是集成了新解析器的版本，可以获取完整的转账信息
 */
router.post('/api/transactions/enhanced', async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['multisigAddress']);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress } = ctx.request.body;
    const multisigPubkey = new PublicKey(multisigAddress);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    console.log(`🔍 获取多签 ${multisigAddress} 的交易列表...`);

    const transactions = [];
    const currentTransactionIndex = Number(multisigData.transactionIndex.toString());
    const startIndex = Math.max(1, currentTransactionIndex - MAX_PROPOSALS_TO_CHECK);

    console.log(`📊 扫描交易索引范围: ${startIndex} - ${currentTransactionIndex}`);

    // 1. 获取基础交易信息（保持原有逻辑）
    for (let i = startIndex; i <= currentTransactionIndex; i++) {
      try {
        const [proposalPda] = multisig.getProposalPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        const proposalAccount = await connection.getAccountInfo(proposalPda);
        if (!proposalAccount) continue;

        const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];

        // 获取交易备注
        const [transactionPda] = multisig.getTransactionPda({
          multisigPda: multisigPubkey,
          transactionIndex: BigInt(i),
          programId: MULTISIG_PROGRAM_ID
        });

        let memo = null;
        try {
          const transactionAccount = await connection.getAccountInfo(transactionPda);
          if (transactionAccount) {
            const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
            // 尝试从 message 中提取 memo（如果有的话）
            // 这里可以根据实际的 memo 存储方式调整
          }
        } catch (err) {
          // 忽略获取 memo 失败
        }

        const approvals = proposalData.approved.length;
        const threshold = multisigData.threshold;

        let status = 'Active';
        if (proposalData.status.__kind === 'Executed') status = 'Executed';
        else if (proposalData.status.__kind === 'Rejected') status = 'Rejected';
        else if (proposalData.status.__kind === 'Cancelled') status = 'Cancelled';
        else if (approvals >= threshold) status = 'Approved';

        const votes = [
          ...proposalData.approved.map(member => ({ member: member.toBase58(), vote: 'Approve' })),
          ...proposalData.rejected.map(member => ({ member: member.toBase58(), vote: 'Reject' }))
        ];

        // 推断创建者
        let realCreator = 'Unknown';
        if (proposalData.creator) {
          realCreator = proposalData.creator.toBase58();
        } else if (proposalData.approved && proposalData.approved.length > 0) {
          realCreator = proposalData.approved[0].toBase58();
        }

        // 获取创建时间
        let createdAt = null;
        try {
          if (proposalData.status && proposalData.status.timestamp) {
            const timestamp = Number(proposalData.status.timestamp.toString());
            createdAt = new Date(timestamp * 1000).toISOString();
          }
        } catch (err) {
          // 使用估算时间
          const baseTime = new Date('2024-01-01T00:00:00Z').getTime();
          const estimatedTime = new Date(baseTime + (i - 1) * 60 * 60 * 1000);
          createdAt = estimatedTime.toISOString();
        }

        // 基础交易信息（不包含解析的转账详情）
        transactions.push({
          transactionIndex: i,
          status,
          approvals,
          threshold,
          creator: realCreator,
          memo,
          votes,
          canExecute: status === 'Approved',
          createdAt,
          // 这些字段将被解析器填充
          transactionType: 'unknown',
          transferAmount: null,
          transferToken: null,
          toAddress: null,
          fromAddress: null,
          tokenMint: null
        });
      } catch (err) {
        console.warn(`跳过交易 ${i}:`, err.message);
      }
    }

    console.log(`📋 获取到 ${transactions.length} 个基础交易信息`);

    // 2. 🎯 使用增强解析器获取详细转账信息
    console.log(`🔧 开始解析转账详情...`);
    const startTime = Date.now();
    
    const enhancedTransactions = await enhanceTransactionList(transactions, multisigAddress);
    
    const endTime = Date.now();
    const parsedCount = enhancedTransactions.filter(tx => tx.isParsed).length;
    
    console.log(`✅ 解析完成! 耗时: ${endTime - startTime}ms`);
    console.log(`📊 解析统计: ${parsedCount}/${enhancedTransactions.length} 个交易成功解析`);

    // 3. 按时间倒序排列
    enhancedTransactions.sort((a, b) => b.transactionIndex - a.transactionIndex);

    // 4. 返回增强后的交易列表
    ctx.body = {
      transactions: enhancedTransactions,
      summary: {
        total: enhancedTransactions.length,
        parsed: parsedCount,
        parseRate: `${(parsedCount / enhancedTransactions.length * 100).toFixed(1)}%`,
        parseTime: `${endTime - startTime}ms`
      }
    };

  } catch (error) {
    console.error('获取增强交易列表失败:', error);
    handleError(ctx, error, '获取交易列表失败');
  }
});

/**
 * 🎯 单个交易详情解析 API
 * 专门用于解析单个交易的详细信息
 */
router.post('/api/transactions/parse', async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['multisigAddress', 'transactionIndex']);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress, transactionIndex } = ctx.request.body;
    
    console.log(`🔍 解析交易详情: ${multisigAddress} #${transactionIndex}`);

    const { parseTransactionDetails } = require('./transaction-parser');
    const result = await parseTransactionDetails(multisigAddress, transactionIndex);

    if (result) {
      console.log(`✅ 解析成功: ${result.type} - ${result.amount} ${result.tokenSymbol || ''}`);
      ctx.body = {
        success: true,
        transaction: {
          transactionIndex: parseInt(transactionIndex),
          ...result
        }
      };
    } else {
      console.log(`⚠️  无法解析交易 #${transactionIndex}`);
      ctx.body = {
        success: false,
        message: '无法解析该交易，可能不是转账交易或账户不存在',
        transaction: null
      };
    }

  } catch (error) {
    console.error('解析交易详情失败:', error);
    handleError(ctx, error, '解析交易详情失败');
  }
});

/**
 * 🎯 批量解析多个交易
 */
router.post('/api/transactions/batch-parse', async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['multisigAddress', 'transactionIndexes']);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress, transactionIndexes } = ctx.request.body;

    if (!Array.isArray(transactionIndexes) || transactionIndexes.length === 0) {
      return handleError(ctx, { status: 400 }, 'transactionIndexes 必须是非空数组');
    }

    console.log(`🔍 批量解析交易: ${multisigAddress} [${transactionIndexes.join(', ')}]`);

    const { parseMultipleTransactions } = require('./transaction-parser');
    const startTime = Date.now();
    
    const results = await parseMultipleTransactions(multisigAddress, transactionIndexes);
    
    const endTime = Date.now();
    
    console.log(`✅ 批量解析完成: ${results.length}/${transactionIndexes.length} 个交易, 耗时: ${endTime - startTime}ms`);

    ctx.body = {
      success: true,
      transactions: results,
      summary: {
        requested: transactionIndexes.length,
        parsed: results.length,
        parseRate: `${(results.length / transactionIndexes.length * 100).toFixed(1)}%`,
        parseTime: `${endTime - startTime}ms`
      }
    };

  } catch (error) {
    console.error('批量解析交易失败:', error);
    handleError(ctx, error, '批量解析交易失败');
  }
});

/**
 * 🎯 获取交易统计信息
 */
router.post('/api/transactions/stats', async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['multisigAddress']);
    if (error) return handleError(ctx, { status: 400 }, error);

    const { multisigAddress } = ctx.request.body;
    const multisigPubkey = new PublicKey(multisigAddress);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    const currentTransactionIndex = Number(multisigData.transactionIndex.toString());
    const startIndex = Math.max(1, currentTransactionIndex - MAX_PROPOSALS_TO_CHECK);
    
    // 生成要分析的交易索引
    const indexesToAnalyze = [];
    for (let i = startIndex; i <= currentTransactionIndex; i++) {
      indexesToAnalyze.push(i);
    }

    console.log(`📊 分析交易统计: ${multisigAddress} [${indexesToAnalyze.length} 个交易]`);

    const { parseMultipleTransactions } = require('./transaction-parser');
    const parsedTransactions = await parseMultipleTransactions(multisigAddress, indexesToAnalyze);

    // 统计分析
    const stats = {
      total: indexesToAnalyze.length,
      parsed: parsedTransactions.length,
      parseRate: `${(parsedTransactions.length / indexesToAnalyze.length * 100).toFixed(1)}%`,
      
      byType: {
        sol: parsedTransactions.filter(tx => tx.type === 'sol').length,
        token: parsedTransactions.filter(tx => tx.type === 'token').length,
        unknown: indexesToAnalyze.length - parsedTransactions.length
      },
      
      totalVolume: {
        sol: parsedTransactions
          .filter(tx => tx.type === 'sol')
          .reduce((sum, tx) => sum + tx.amount, 0),
        tokens: parsedTransactions
          .filter(tx => tx.type === 'token')
          .reduce((acc, tx) => {
            const symbol = tx.tokenSymbol || 'Unknown';
            acc[symbol] = (acc[symbol] || 0) + tx.amount;
            return acc;
          }, {})
      },
      
      recentTransactions: parsedTransactions
        .sort((a, b) => b.transactionIndex - a.transactionIndex)
        .slice(0, 5)
        .map(tx => ({
          index: tx.transactionIndex,
          type: tx.type,
          amount: tx.amount,
          token: tx.tokenSymbol,
          to: tx.toAddress?.substring(0, 8) + '...',
          createdAt: tx.createdAt
        }))
    };

    console.log(`📈 统计完成: ${stats.parsed}/${stats.total} 解析成功`);

    ctx.body = { stats };

  } catch (error) {
    console.error('获取交易统计失败:', error);
    handleError(ctx, error, '获取交易统计失败');
  }
});

module.exports = router;
