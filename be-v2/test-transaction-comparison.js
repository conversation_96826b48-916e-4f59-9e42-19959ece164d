/**
 * 🔍 交易对比分析测试
 * 
 * 专门分析 #43 (官方平台创建) 和 #44 (自建平台创建) 的差异
 * 找出 #43 解析失败的原因
 */

const multisig = require('@sqds/multisig');
const { PublicKey, SystemProgram } = require('@solana/web3.js');
const { connection, MULTISIG_PROGRAM_ID } = require('./utils');

/**
 * 详细分析单个交易的结构
 */
async function analyzeTransactionStructure(multisigAddress, transactionIndex, description) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🔍 分析交易 #${transactionIndex} (${description})`);
  console.log(`${'='.repeat(60)}`);

  try {
    const multisigPubkey = new PublicKey(multisigAddress);

    // 1. 获取 Proposal 数据
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    console.log(`📋 Proposal PDA: ${proposalPda.toBase58()}`);

    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (!proposalAccount) {
      console.log('❌ Proposal 账户不存在');
      return null;
    }

    const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
    console.log(`✅ Proposal 状态: ${proposalData.status.__kind}`);
    console.log(`👤 创建者: ${proposalData.creator?.toBase58() || 'Unknown'}`);

    // 2. 获取 VaultTransaction 数据
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      index: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    console.log(`📋 Transaction PDA: ${transactionPda.toBase58()}`);

    const transactionAccount = await connection.getAccountInfo(transactionPda);
    if (!transactionAccount) {
      console.log('❌ VaultTransaction 账户不存在');
      return null;
    }

    const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
    const message = vaultTransactionData.message;

    console.log(`\n📊 交易消息分析:`);
    console.log(`   账户数量: ${message.accountKeys.length}`);
    console.log(`   指令数量: ${message.instructions.length}`);

    // 3. 详细分析每个账户
    console.log(`\n📝 账户列表:`);
    message.accountKeys.forEach((key, index) => {
      const keyStr = key.toBase58();
      let description = '';
      
      // 识别特殊账户
      if (keyStr === SystemProgram.programId.toBase58()) {
        description = ' (SystemProgram)';
      } else if (keyStr === MULTISIG_PROGRAM_ID.toBase58()) {
        description = ' (Squads Program)';
      } else if (keyStr === '11111111111111111111111111111111') {
        description = ' (SystemProgram)';
      } else if (keyStr.startsWith('ComputeBudget')) {
        description = ' (Compute Budget)';
      }
      
      console.log(`   [${index}] ${keyStr}${description}`);
    });

    // 4. 详细分析每个指令
    console.log(`\n🔧 指令详细分析:`);
    
    for (let i = 0; i < message.instructions.length; i++) {
      const instruction = message.instructions[i];
      
      console.log(`\n--- 指令 #${i + 1} ---`);
      console.log(`程序ID索引: ${instruction.programIdIndex}`);
      
      if (instruction.programIdIndex < message.accountKeys.length) {
        const programId = message.accountKeys[instruction.programIdIndex];
        console.log(`程序ID: ${programId.toBase58()}`);
        
        // 识别程序类型
        if (programId.equals(SystemProgram.programId)) {
          console.log(`程序类型: SystemProgram ✅`);
        } else if (programId.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
          console.log(`程序类型: Squads Program`);
        } else if (programId.toBase58() === 'ComputeBudget111111111111111111111111111111') {
          console.log(`程序类型: Compute Budget Program`);
        } else {
          console.log(`程序类型: 未知程序`);
        }
      } else {
        console.log(`❌ 程序ID索引超出范围: ${instruction.programIdIndex} >= ${message.accountKeys.length}`);
        continue;
      }

      // 分析账户索引
      console.log(`账户索引类型: ${typeof instruction.accountIndexes}`);
      console.log(`账户索引原始数据:`, instruction.accountIndexes);
      
      let accountIndexes = [];
      if (instruction.accountKeyIndexes) {
        accountIndexes = instruction.accountKeyIndexes;
        console.log(`使用 accountKeyIndexes: [${accountIndexes.join(', ')}]`);
      } else if (instruction.accountIndexes) {
        if (Array.isArray(instruction.accountIndexes)) {
          accountIndexes = instruction.accountIndexes;
        } else if (typeof instruction.accountIndexes === 'object') {
          accountIndexes = Object.values(instruction.accountIndexes);
        }
        console.log(`使用 accountIndexes: [${accountIndexes.join(', ')}]`);
      }

      // 显示涉及的账户
      if (accountIndexes.length > 0) {
        console.log(`涉及的账户:`);
        accountIndexes.forEach((index, pos) => {
          if (index < message.accountKeys.length) {
            console.log(`   [${pos}] -> 账户[${index}]: ${message.accountKeys[index].toBase58()}`);
          } else {
            console.log(`   [${pos}] -> ❌ 索引超出范围: ${index}`);
          }
        });
      }

      // 分析指令数据
      console.log(`数据类型: ${typeof instruction.data}`);
      console.log(`数据长度: ${instruction.data ? Object.keys(instruction.data).length : 0} bytes`);
      
      if (instruction.data && Object.keys(instruction.data).length > 0) {
        const dataBuffer = Buffer.from(Object.values(instruction.data));
        console.log(`数据 (hex): ${dataBuffer.toString('hex').substring(0, 32)}${dataBuffer.length > 16 ? '...' : ''}`);
        
        // 如果是 SystemProgram，尝试解析
        const programId = message.accountKeys[instruction.programIdIndex];
        if (programId && programId.equals(SystemProgram.programId)) {
          console.log(`🎯 这是 SystemProgram 指令，尝试解析:`);
          
          if (dataBuffer.length >= 4) {
            const instructionType = dataBuffer.readUInt32LE(0);
            console.log(`   指令类型: ${instructionType} ${instructionType === 2 ? '(Transfer)' : ''}`);
            
            if (instructionType === 2 && dataBuffer.length >= 12) {
              const amount = dataBuffer.readBigUInt64LE(4);
              console.log(`   转账金额: ${amount} lamports (${Number(amount) / 1e9} SOL)`);
              
              if (accountIndexes.length >= 2) {
                const fromIndex = accountIndexes[0];
                const toIndex = accountIndexes[1];
                console.log(`   从地址: ${message.accountKeys[fromIndex]?.toBase58() || 'Invalid'}`);
                console.log(`   到地址: ${message.accountKeys[toIndex]?.toBase58() || 'Invalid'}`);
              }
            }
          }
        }
      }
    }

    return {
      proposalData,
      vaultTransactionData,
      message,
      analysis: {
        accountCount: message.accountKeys.length,
        instructionCount: message.instructions.length,
        hasSystemProgram: message.accountKeys.some(key => key.equals(SystemProgram.programId))
      }
    };

  } catch (error) {
    console.error(`❌ 分析交易 #${transactionIndex} 失败:`, error.message);
    return null;
  }
}

/**
 * 对比两个交易的差异
 */
function compareTransactions(tx43, tx44) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 交易对比分析`);
  console.log(`${'='.repeat(60)}`);

  if (!tx43 || !tx44) {
    console.log('❌ 无法对比，缺少交易数据');
    return;
  }

  console.log(`\n🔍 基本信息对比:`);
  console.log(`   #43 账户数量: ${tx43.analysis.accountCount}`);
  console.log(`   #44 账户数量: ${tx44.analysis.accountCount}`);
  console.log(`   #43 指令数量: ${tx43.analysis.instructionCount}`);
  console.log(`   #44 指令数量: ${tx44.analysis.instructionCount}`);

  console.log(`\n🔍 程序对比:`);
  console.log(`   #43 包含 SystemProgram: ${tx43.analysis.hasSystemProgram ? '是' : '否'}`);
  console.log(`   #44 包含 SystemProgram: ${tx44.analysis.hasSystemProgram ? '是' : '否'}`);

  // 找出 SystemProgram 指令的位置
  const findSystemInstructions = (message) => {
    const systemInstructions = [];
    message.instructions.forEach((instruction, index) => {
      const programId = message.accountKeys[instruction.programIdIndex];
      if (programId && programId.equals(SystemProgram.programId)) {
        systemInstructions.push({
          index,
          programIdIndex: instruction.programIdIndex,
          accountIndexes: instruction.accountIndexes || instruction.accountKeyIndexes,
          dataLength: instruction.data ? Object.keys(instruction.data).length : 0
        });
      }
    });
    return systemInstructions;
  };

  const tx43SystemInstructions = findSystemInstructions(tx43.message);
  const tx44SystemInstructions = findSystemInstructions(tx44.message);

  console.log(`\n🎯 SystemProgram 指令对比:`);
  console.log(`   #43 SystemProgram 指令: ${tx43SystemInstructions.length} 个`);
  tx43SystemInstructions.forEach((inst, i) => {
    console.log(`     指令${i + 1}: 位置${inst.index}, 程序ID索引${inst.programIdIndex}, 数据长度${inst.dataLength}`);
  });

  console.log(`   #44 SystemProgram 指令: ${tx44SystemInstructions.length} 个`);
  tx44SystemInstructions.forEach((inst, i) => {
    console.log(`     指令${i + 1}: 位置${inst.index}, 程序ID索引${inst.programIdIndex}, 数据长度${inst.dataLength}`);
  });

  // 分析差异原因
  console.log(`\n💡 差异分析:`);
  if (tx43SystemInstructions.length === 0) {
    console.log(`   ❌ #43 没有找到 SystemProgram 指令`);
    console.log(`   🔍 可能原因: SystemProgram 在不同的账户索引位置`);
  } else if (tx44SystemInstructions.length === 0) {
    console.log(`   ❌ #44 没有找到 SystemProgram 指令`);
  } else {
    console.log(`   ✅ 两个交易都包含 SystemProgram 指令`);
    console.log(`   🔍 需要检查指令解析逻辑`);
  }
}

/**
 * 测试修复后的解析逻辑
 */
async function testFixedParser(multisigAddress, transactionIndex) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🔧 测试修复后的解析逻辑 - 交易 #${transactionIndex}`);
  console.log(`${'='.repeat(60)}`);

  try {
    const multisigPubkey = new PublicKey(multisigAddress);

    // 获取交易数据
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      index: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    const transactionAccount = await connection.getAccountInfo(transactionPda);
    const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
    const message = vaultTransactionData.message;

    // 改进的解析逻辑
    for (let i = 0; i < message.instructions.length; i++) {
      const instruction = message.instructions[i];
      
      console.log(`\n🔍 检查指令 #${i + 1}:`);
      
      // 安全检查程序ID索引
      if (instruction.programIdIndex >= message.accountKeys.length) {
        console.log(`   ❌ 程序ID索引超出范围: ${instruction.programIdIndex} >= ${message.accountKeys.length}`);
        continue;
      }

      const programId = message.accountKeys[instruction.programIdIndex];
      console.log(`   程序ID: ${programId.toBase58()}`);

      // 检查是否是 SystemProgram
      if (programId.equals(SystemProgram.programId)) {
        console.log(`   ✅ 找到 SystemProgram 指令!`);
        
        // 处理账户索引
        let accountIndexes = [];
        if (instruction.accountKeyIndexes) {
          accountIndexes = instruction.accountKeyIndexes;
        } else if (instruction.accountIndexes) {
          if (Array.isArray(instruction.accountIndexes)) {
            accountIndexes = instruction.accountIndexes;
          } else if (typeof instruction.accountIndexes === 'object') {
            accountIndexes = Object.values(instruction.accountIndexes);
          }
        }

        console.log(`   账户索引: [${accountIndexes.join(', ')}]`);

        // 处理指令数据
        if (instruction.data && Object.keys(instruction.data).length >= 12) {
          const dataBuffer = Buffer.from(Object.values(instruction.data));
          const instructionType = dataBuffer.readUInt32LE(0);
          
          console.log(`   指令类型: ${instructionType}`);
          
          if (instructionType === 2) { // Transfer
            const amount = dataBuffer.readBigUInt64LE(4);
            console.log(`   ✅ 这是转账指令!`);
            console.log(`   转账金额: ${amount} lamports (${Number(amount) / 1e9} SOL)`);
            
            if (accountIndexes.length >= 2) {
              const fromIndex = accountIndexes[0];
              const toIndex = accountIndexes[1];
              
              if (fromIndex < message.accountKeys.length && toIndex < message.accountKeys.length) {
                const fromAddress = message.accountKeys[fromIndex].toBase58();
                const toAddress = message.accountKeys[toIndex].toBase58();
                
                console.log(`   从地址: ${fromAddress}`);
                console.log(`   到地址: ${toAddress}`);
                
                console.log(`\n🎉 解析成功!`);
                return {
                  type: 'sol',
                  amount: Number(amount) / 1e9,
                  fromAddress,
                  toAddress,
                  tokenSymbol: 'SOL'
                };
              } else {
                console.log(`   ❌ 账户索引超出范围`);
              }
            } else {
              console.log(`   ❌ 账户索引不足`);
            }
          }
        } else {
          console.log(`   ❌ 指令数据不足`);
        }
      }
    }

    console.log(`\n❌ 未找到可解析的转账指令`);
    return null;

  } catch (error) {
    console.error(`❌ 解析失败:`, error.message);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runComparisonTest() {
  console.log('🚀 开始交易对比分析测试...\n');

  const multisigAddress = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr';

  try {
    // 1. 分析 #43 交易（官方平台创建，解析失败）
    const tx43 = await analyzeTransactionStructure(multisigAddress, 43, '官方平台创建，解析失败');

    // 2. 分析 #44 交易（自建平台创建，解析成功）
    const tx44 = await analyzeTransactionStructure(multisigAddress, 44, '自建平台创建，解析成功');

    // 3. 对比分析
    compareTransactions(tx43, tx44);

    // 4. 测试修复后的解析逻辑
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🔧 测试修复后的解析逻辑`);
    console.log(`${'='.repeat(60)}`);

    const result43 = await testFixedParser(multisigAddress, 43);
    const result44 = await testFixedParser(multisigAddress, 44);

    console.log(`\n📊 最终结果:`);
    console.log(`   #43 解析结果:`, result43 ? '✅ 成功' : '❌ 失败');
    console.log(`   #44 解析结果:`, result44 ? '✅ 成功' : '❌ 失败');

    if (result43) {
      console.log(`   #43 详情: ${result43.amount} ${result43.tokenSymbol} -> ${result43.toAddress.substring(0, 8)}...`);
    }
    if (result44) {
      console.log(`   #44 详情: ${result44.amount} ${result44.tokenSymbol} -> ${result44.toAddress.substring(0, 8)}...`);
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  runComparisonTest().then(() => {
    console.log('\n🎉 交易对比分析完成!');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  analyzeTransactionStructure,
  compareTransactions,
  testFixedParser,
  runComparisonTest
};
