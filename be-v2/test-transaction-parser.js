const multisig = require('@sqds/multisig');
const { PublicKey, SystemProgram } = require('@solana/web3.js');
const { connection, MULTISIG_PROGRAM_ID } = require('./utils');

/**
 * 解析 VaultTransaction 中的转账信息
 * 这是获取转账金额、目标地址的核心函数
 */
async function parseTransactionDetails(multisigAddress, transactionIndex) {
  try {
    console.log(`\n=== 解析交易详情 ===`);
    console.log(`多签地址: ${multisigAddress}`);
    console.log(`交易索引: ${transactionIndex}`);

    const multisigPubkey = new PublicKey(multisigAddress);

    // 1. 获取 Proposal 数据（包含状态和时间戳）
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    console.log(`Proposal PDA: ${proposalPda.toBase58()}`);

    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (!proposalAccount) {
      throw new Error('Proposal 账户不存在');
    }

    const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];
    console.log(`Proposal 状态:`, proposalData.status);
    console.log(`创建者:`, proposalData.creator?.toBase58());

    // 2. 获取 VaultTransaction 数据（包含实际的交易指令）
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      index: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    console.log(`Transaction PDA: ${transactionPda.toBase58()}`);

    const transactionAccount = await connection.getAccountInfo(transactionPda);
    if (!transactionAccount) {
      throw new Error('VaultTransaction 账户不存在');
    }

    const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
    console.log(`VaultTransaction 创建者:`, vaultTransactionData.creator.toBase58());
    console.log(`Vault 索引:`, vaultTransactionData.vaultIndex);

    // 3. 解析交易消息中的指令
    const message = vaultTransactionData.message;
    console.log(`\n=== 交易消息分析 ===`);
    console.log(`账户数量:`, message.accountKeys.length);
    console.log(`指令数量:`, message.instructions.length);

    // 打印所有账户
    console.log(`\n账户列表:`);
    message.accountKeys.forEach((key, index) => {
      console.log(`  [${index}] ${key.toBase58()}`);
    });

    // 4. 解析每个指令
    const transferInfo = {
      type: 'unknown',
      amount: null,
      toAddress: null,
      fromAddress: null,
      tokenMint: null,
      createdAt: null
    };

    console.log(`\n=== 指令分析 ===`);
    for (let i = 0; i < message.instructions.length; i++) {
      const instruction = message.instructions[i];
      console.log(`\n指令 ${i}:`);
      console.log(`  程序ID索引: ${instruction.programIdIndex}`);
      const programId = message.accountKeys[instruction.programIdIndex];
      console.log(`  程序ID: ${programId ? programId.toBase58() : 'undefined'}`);
      // 处理账户索引（可能是数组或对象）
      let accountIndexes = [];
      if (instruction.accountKeyIndexes) {
        accountIndexes = instruction.accountKeyIndexes;
      } else if (instruction.accountIndexes) {
        accountIndexes = Object.values(instruction.accountIndexes);
      }
      console.log(`  账户索引: [${accountIndexes.join(', ')}]`);
      console.log(`  数据长度: ${instruction.data ? instruction.data.length : 0} bytes`);
      console.log(`  数据类型: ${instruction.data ? typeof instruction.data : 'undefined'}`);
      console.log(`  数据 (hex): ${instruction.data ? Buffer.from(instruction.data).toString('hex') : 'no data'}`);
      console.log(`  指令结构:`, JSON.stringify(instruction, null, 2));

      // 检查是否是 SystemProgram.transfer 指令
      if (programId && programId.equals(SystemProgram.programId)) {
        console.log(`  -> 这是 SystemProgram 指令`);

        // SystemProgram.transfer 的指令数据格式：
        // [0-3]: 指令类型 (2 = transfer)
        // [4-11]: 转账金额 (u64, little-endian)
        if (instruction.data && Object.keys(instruction.data).length >= 12) {
          // 将数据对象转换为 Buffer
          const dataBuffer = Buffer.from(Object.values(instruction.data));
          const instructionType = dataBuffer.readUInt32LE(0);
          console.log(`  -> 指令类型: ${instructionType}`);

          if (instructionType === 2) { // SystemProgram.transfer
            const amount = dataBuffer.readBigUInt64LE(4);
            console.log(`  -> SOL 转账金额: ${amount} lamports (${Number(amount) / 1e9} SOL)`);

            // 获取转账的 from 和 to 地址
            if (accountIndexes.length >= 2) {
              const fromIndex = accountIndexes[0];
              const toIndex = accountIndexes[1];
              const fromAddress = message.accountKeys[fromIndex].toBase58();
              const toAddress = message.accountKeys[toIndex].toBase58();

              console.log(`  -> 从地址: ${fromAddress}`);
              console.log(`  -> 到地址: ${toAddress}`);

              transferInfo.type = 'sol';
              transferInfo.amount = Number(amount) / 1e9;
              transferInfo.fromAddress = fromAddress;
              transferInfo.toAddress = toAddress;
            }
          }
        }
      } else if (programId && programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
        console.log(`  -> 这是 Token Program 指令`);

        // SPL Token Transfer 指令格式：
        // [0]: 指令类型 (3 = Transfer)
        // [1-8]: 转账金额 (u64, little-endian)
        if (instruction.data && Object.keys(instruction.data).length >= 9) {
          const dataBuffer = Buffer.from(Object.values(instruction.data));
          const instructionType = dataBuffer[0];
          console.log(`  -> Token 指令类型: ${instructionType}`);

          if (instructionType === 3) { // Token.transfer
            const amount = dataBuffer.readBigUInt64LE(1);
            console.log(`  -> Token 转账金额: ${amount} (原始单位)`);

            // Token 转账需要 3 个账户：source, destination, authority
            if (accountIndexes.length >= 3) {
              const sourceIndex = accountIndexes[0];
              const destIndex = accountIndexes[1];
              const authorityIndex = accountIndexes[2];

              const sourceAddress = message.accountKeys[sourceIndex].toBase58();
              const destAddress = message.accountKeys[destIndex].toBase58();
              const authorityAddress = message.accountKeys[authorityIndex].toBase58();

              console.log(`  -> 源 Token 账户: ${sourceAddress}`);
              console.log(`  -> 目标 Token 账户: ${destAddress}`);
              console.log(`  -> 授权账户: ${authorityAddress}`);

              // 需要进一步查询 Token 账户信息来获取 mint 和实际接收地址
              try {
                const destTokenAccount = await connection.getParsedAccountInfo(new PublicKey(destAddress));
                if (destTokenAccount.value && destTokenAccount.value.data.parsed) {
                  const parsedInfo = destTokenAccount.value.data.parsed.info;
                  const tokenMint = parsedInfo.mint;
                  const ownerAddress = parsedInfo.owner;
                  const decimals = parsedInfo.tokenAmount.decimals;

                  console.log(`  -> Token Mint: ${tokenMint}`);
                  console.log(`  -> 实际接收者: ${ownerAddress}`);
                  console.log(`  -> Token 精度: ${decimals}`);

                  transferInfo.type = 'token';
                  transferInfo.amount = Number(amount) / Math.pow(10, decimals);
                  transferInfo.toAddress = ownerAddress;
                  transferInfo.tokenMint = tokenMint;
                  transferInfo.fromAddress = authorityAddress;
                }
              } catch (tokenError) {
                console.log(`  -> 获取 Token 账户信息失败: ${tokenError.message}`);
              }
            }
          }
        }
      } else {
        console.log(`  -> 未知程序: ${programId ? programId.toBase58() : 'undefined'}`);
      }
    }

    // 5. 获取创建时间
    if (proposalData.status && proposalData.status.timestamp) {
      const timestamp = Number(proposalData.status.timestamp.toString());
      transferInfo.createdAt = new Date(timestamp * 1000).toISOString();
      console.log(`\n创建时间: ${transferInfo.createdAt}`);
    }

    console.log(`\n=== 解析结果 ===`);
    console.log(JSON.stringify(transferInfo, null, 2));

    return transferInfo;

  } catch (error) {
    console.error('解析交易详情失败:', error);
    throw error;
  }
}

/**
 * 测试函数：获取多签账户的所有交易并解析
 */
async function testTransactionParser() {
  try {
    // 使用 constants.js 中配置的多签地址
    const { FIXED_MULTISIG_ADDRESS } = require('./constants');
    const multisigAddress = FIXED_MULTISIG_ADDRESS;

    console.log('=== Squads 交易解析器测试 ===');
    console.log(`测试多签地址: ${multisigAddress}`);

    // 获取多签信息
    const multisigPubkey = new PublicKey(multisigAddress);
    const multisigData = await multisig.accounts.Multisig.fromAccountAddress(connection, multisigPubkey);

    console.log(`当前交易索引: ${multisigData.transactionIndex}`);
    console.log(`成员数量: ${multisigData.members.length}`);
    console.log(`投票阈值: ${multisigData.threshold}`);

    // 测试解析最近的几个交易
    const currentIndex = Number(multisigData.transactionIndex.toString());
    const testIndexes = [];

    // 测试最近的 3 个交易
    for (let i = Math.max(1, currentIndex - 2); i <= currentIndex; i++) {
      testIndexes.push(i);
    }

    console.log(`\n将测试交易索引: [${testIndexes.join(', ')}]`);

    for (const index of testIndexes) {
      try {
        console.log(`\n${'='.repeat(50)}`);
        const result = await parseTransactionDetails(multisigAddress, index);

        if (result.type !== 'unknown') {
          console.log(`✅ 成功解析交易 #${index}:`);
          console.log(`   类型: ${result.type}`);
          console.log(`   金额: ${result.amount}`);
          console.log(`   目标地址: ${result.toAddress}`);
          console.log(`   创建时间: ${result.createdAt}`);
        } else {
          console.log(`⚠️  交易 #${index} 不是标准转账交易`);
        }
      } catch (error) {
        console.log(`❌ 解析交易 #${index} 失败: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testTransactionParser().then(() => {
    console.log('\n测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = {
  parseTransactionDetails,
  testTransactionParser
};
