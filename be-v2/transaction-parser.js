const multisig = require('@sqds/multisig');
const { PublicKey, SystemProgram } = require('@solana/web3.js');
const { connection, MULTISIG_PROGRAM_ID } = require('./utils');

/**
 * 增强的交易解析器 - 从 VaultTransaction 中提取完整的转账信息
 * 这是解决 squads 平台获取转账金额、目标地址、创建时间的核心解决方案
 */

/**
 * 解析单个交易的详细信息
 * @param {string} multisigAddress - 多签地址
 * @param {number} transactionIndex - 交易索引
 * @returns {Object} 解析后的交易信息
 */
async function parseTransactionDetails(multisigAddress, transactionIndex) {
  try {
    const multisigPubkey = new PublicKey(multisigAddress);

    // 1. 获取 Proposal 数据
    const [proposalPda] = multisig.getProposalPda({
      multisigPda: multisigPubkey,
      transactionIndex: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    const proposalAccount = await connection.getAccountInfo(proposalPda);
    if (!proposalAccount) {
      return null;
    }

    const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0];

    // 2. 获取 VaultTransaction 数据
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: multisigPubkey,
      index: BigInt(transactionIndex),
      programId: MULTISIG_PROGRAM_ID
    });

    const transactionAccount = await connection.getAccountInfo(transactionPda);
    if (!transactionAccount) {
      return null;
    }

    const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0];
    const message = vaultTransactionData.message;

    // 3. 解析交易指令
    const transferInfo = {
      type: 'unknown',
      amount: null,
      toAddress: null,
      fromAddress: null,
      tokenMint: null,
      tokenSymbol: null,
      decimals: null,
      createdAt: null,
      creator: proposalData.creator?.toBase58() || 'Unknown'
    };

    // 解析每个指令
    for (const instruction of message.instructions) {
      const programId = message.accountKeys[instruction.programIdIndex];

      // 解析 SOL 转账
      if (programId.equals(SystemProgram.programId)) {
        const solTransfer = await parseSystemTransfer(instruction, message);

        if (solTransfer) {
          Object.assign(transferInfo, solTransfer);
          break;
        }
      }
      // 解析 SPL Token 转账
      else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
        const tokenTransfer = await parseTokenTransfer(instruction, message);

        if (tokenTransfer) {
          Object.assign(transferInfo, tokenTransfer);
          break;
        }
      }
    }

    // 4. 获取创建时间
    if (proposalData.status && proposalData.status.timestamp) {
      const timestamp = Number(proposalData.status.timestamp.toString());
      transferInfo.createdAt = new Date(timestamp * 1000).toISOString();
    }

    return transferInfo;

  } catch (error) {
    console.error(`解析交易 ${transactionIndex} 失败:`, error.message);
    return null;
  }
}

/**
 * 解析 SystemProgram.transfer 指令
 */
async function parseSystemTransfer(instruction, message) {
  try {
    // 处理数据格式（可能是对象或Buffer）
    let dataBuffer;
    if (instruction.data && typeof instruction.data === 'object' && !Buffer.isBuffer(instruction.data)) {
      dataBuffer = Buffer.from(Object.values(instruction.data));
    } else if (Buffer.isBuffer(instruction.data)) {
      dataBuffer = instruction.data;
    } else {
      return null;
    }

    if (dataBuffer.length < 12) return null;

    const instructionType = dataBuffer.readUInt32LE(0);
    if (instructionType !== 2) return null; // 不是 transfer 指令

    const amount = dataBuffer.readBigUInt64LE(4);

    // 处理账户索引（可能是数组或对象）
    let accountIndexes = [];
    if (instruction.accountKeyIndexes) {
      accountIndexes = instruction.accountKeyIndexes;
    } else if (instruction.accountIndexes) {
      accountIndexes = Object.values(instruction.accountIndexes);
    }

    if (accountIndexes.length < 2) return null;

    const fromIndex = accountIndexes[0];
    const toIndex = accountIndexes[1];
    const fromAddress = message.accountKeys[fromIndex].toBase58();
    const toAddress = message.accountKeys[toIndex].toBase58();

    return {
      type: 'sol',
      amount: Number(amount) / 1e9,
      fromAddress,
      toAddress,
      tokenSymbol: 'SOL',
      decimals: 9
    };
  } catch (error) {
    return null;
  }
}

/**
 * 解析 Token.transfer 指令
 */
async function parseTokenTransfer(instruction, message) {
  try {
    // 处理数据格式（可能是对象或Buffer）
    let dataBuffer;
    if (instruction.data && typeof instruction.data === 'object' && !Buffer.isBuffer(instruction.data)) {
      dataBuffer = Buffer.from(Object.values(instruction.data));
    } else if (Buffer.isBuffer(instruction.data)) {
      dataBuffer = instruction.data;
    } else {
      return null;
    }

    if (dataBuffer.length < 9) return null;

    const instructionType = dataBuffer[0];
    if (instructionType !== 3) return null; // 不是 transfer 指令

    const amount = dataBuffer.readBigUInt64LE(1);

    // 处理账户索引（可能是数组或对象）
    let accountIndexes = [];
    if (instruction.accountKeyIndexes) {
      accountIndexes = instruction.accountKeyIndexes;
    } else if (instruction.accountIndexes) {
      accountIndexes = Object.values(instruction.accountIndexes);
    }

    if (accountIndexes.length < 3) return null;

    const sourceIndex = accountIndexes[0];
    const destIndex = accountIndexes[1];
    const authorityIndex = accountIndexes[2];

    const sourceAddress = message.accountKeys[sourceIndex].toBase58();
    const destAddress = message.accountKeys[destIndex].toBase58();
    const authorityAddress = message.accountKeys[authorityIndex].toBase58();

    // 获取目标 Token 账户信息
    const destTokenAccount = await connection.getParsedAccountInfo(new PublicKey(destAddress));
    if (!destTokenAccount.value || !destTokenAccount.value.data.parsed) {
      return null;
    }

    const parsedInfo = destTokenAccount.value.data.parsed.info;
    const tokenMint = parsedInfo.mint;
    const ownerAddress = parsedInfo.owner;
    const decimals = parsedInfo.tokenAmount.decimals;

    // 尝试获取 Token 符号（可以扩展为查询 Token 注册表）
    const tokenSymbol = getTokenSymbol(tokenMint);

    return {
      type: 'token',
      amount: Number(amount) / Math.pow(10, decimals),
      fromAddress: authorityAddress,
      toAddress: ownerAddress,
      tokenMint,
      tokenSymbol,
      decimals
    };
  } catch (error) {
    return null;
  }
}

/**
 * 获取 Token 符号（可以扩展为查询链上数据或 Token 注册表）
 */
function getTokenSymbol(mint) {
  // 常见 Token 的映射
  const TOKEN_SYMBOLS = {
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
    'So11111111111111111111111111111111111111112': 'WSOL',
    // 可以添加更多 Token
  };

  return TOKEN_SYMBOLS[mint] || `Token(${mint.slice(0, 4)}...${mint.slice(-4)})`;
}

/**
 * 批量解析多个交易
 * @param {string} multisigAddress - 多签地址
 * @param {number[]} transactionIndexes - 交易索引数组
 * @returns {Object[]} 解析后的交易信息数组
 */
async function parseMultipleTransactions(multisigAddress, transactionIndexes) {
  const results = [];

  for (const index of transactionIndexes) {
    const result = await parseTransactionDetails(multisigAddress, index);
    if (result) {
      results.push({
        transactionIndex: index,
        ...result
      });
    }
  }

  return results;
}

/**
 * 增强现有的交易列表，添加解析后的转账信息
 * @param {Object[]} transactions - 现有的交易列表
 * @param {string} multisigAddress - 多签地址
 * @returns {Object[]} 增强后的交易列表
 */
async function enhanceTransactionList(transactions, multisigAddress) {
  const enhanced = [];

  for (const tx of transactions) {
    const parsedDetails = await parseTransactionDetails(multisigAddress, tx.transactionIndex);

    if (parsedDetails && parsedDetails.type !== 'unknown') {
      // 使用解析出的详细信息替换原有的推测信息
      enhanced.push({
        ...tx,
        transactionType: parsedDetails.type,
        transferAmount: parsedDetails.amount,
        transferToken: parsedDetails.tokenSymbol,
        toAddress: parsedDetails.toAddress,
        fromAddress: parsedDetails.fromAddress,
        tokenMint: parsedDetails.tokenMint,
        createdAt: parsedDetails.createdAt || tx.createdAt,
        creator: parsedDetails.creator || tx.creator,
        // 标记为已解析
        isParsed: true,
        parseSource: 'vaultTransaction'
      });
    } else {
      // 保持原有信息，标记为未解析
      enhanced.push({
        ...tx,
        isParsed: false,
        parseSource: 'memo'
      });
    }
  }

  return enhanced;
}

module.exports = {
  parseTransactionDetails,
  parseMultipleTransactions,
  enhanceTransactionList,
  parseSystemTransfer,
  parseTokenTransfer,
  getTokenSymbol
};
