/**
 * 🎯 修复验证测试
 * 
 * 验证 #43 交易解析修复是否成功
 */

const { parseTransactionDetails } = require('./transaction-parser');

/**
 * 验证修复效果
 */
async function verifyFix() {
  console.log('🎯 验证 #43 交易解析修复\n');

  const multisigAddress = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr';

  // 测试关键交易
  const testCases = [
    { index: 43, description: '官方平台创建 (之前失败)' },
    { index: 44, description: '自建平台创建 (之前成功)' },
    { index: 45, description: '自建平台创建 (验证兼容性)' }
  ];

  let successCount = 0;
  let totalCount = testCases.length;

  for (const testCase of testCases) {
    console.log(`🔍 测试交易 #${testCase.index} (${testCase.description}):`);
    
    try {
      const result = await parseTransactionDetails(multisigAddress, testCase.index);
      
      if (result && result.type !== 'unknown') {
        console.log(`   ✅ 解析成功!`);
        console.log(`   类型: ${result.type}`);
        console.log(`   金额: ${result.amount} ${result.tokenSymbol}`);
        console.log(`   从地址: ${result.fromAddress?.substring(0, 8)}...`);
        console.log(`   到地址: ${result.toAddress?.substring(0, 8)}...`);
        console.log(`   创建时间: ${result.createdAt}`);
        successCount++;
      } else {
        console.log(`   ❌ 解析失败`);
      }
    } catch (error) {
      console.log(`   ❌ 解析出错: ${error.message}`);
    }
    
    console.log(''); // 空行分隔
  }

  // 总结
  console.log(`${'='.repeat(50)}`);
  console.log(`📊 修复验证结果:`);
  console.log(`   成功: ${successCount}/${totalCount} (${(successCount/totalCount*100).toFixed(1)}%)`);
  
  if (successCount === totalCount) {
    console.log(`   🎉 所有测试通过！修复成功！`);
    console.log(`   ✅ #43 交易现在可以正常解析`);
    console.log(`   ✅ 兼容官方和自建平台创建的交易`);
    console.log(`   ✅ 不影响现有的解析功能`);
  } else {
    console.log(`   ⚠️  部分测试失败，需要进一步调试`);
  }

  return successCount === totalCount;
}

/**
 * 性能测试
 */
async function performanceTest() {
  console.log(`\n⚡ 性能测试:`);
  
  const multisigAddress = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr';
  const testIndexes = [43, 44, 45];
  
  const startTime = Date.now();
  
  for (const index of testIndexes) {
    await parseTransactionDetails(multisigAddress, index);
  }
  
  const endTime = Date.now();
  const avgTime = (endTime - startTime) / testIndexes.length;
  
  console.log(`   总耗时: ${endTime - startTime}ms`);
  console.log(`   平均耗时: ${avgTime.toFixed(1)}ms/交易`);
  console.log(`   性能: ${avgTime < 2000 ? '✅ 良好' : '⚠️  需要优化'}`);
}

/**
 * 主测试函数
 */
async function runVerification() {
  console.log('🚀 开始修复验证测试...\n');

  try {
    // 1. 功能验证
    const isFixed = await verifyFix();
    
    // 2. 性能测试
    if (isFixed) {
      await performanceTest();
    }

    // 3. 总结
    console.log(`\n${'='.repeat(60)}`);
    if (isFixed) {
      console.log(`🎉 修复验证完成！`);
      console.log(`\n📋 修复总结:`);
      console.log(`   🔧 问题: 官方 Squads 平台创建的交易 programIdIndex 超出范围`);
      console.log(`   💡 原因: VaultTransaction.message.accountKeys 不包含 SystemProgram 引用`);
      console.log(`   ✅ 解决: 添加兼容性解析逻辑 parseSystemTransferFallback`);
      console.log(`   🎯 效果: #43 交易现在可以正常解析转账信息`);
      console.log(`\n现在你的 Squads 平台完全兼容官方和自建平台的交易！`);
    } else {
      console.log(`❌ 修复验证失败，需要进一步调试`);
    }

  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
  }
}

// 运行验证
if (require.main === module) {
  runVerification().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('验证失败:', error);
    process.exit(1);
  });
}

module.exports = {
  verifyFix,
  performanceTest,
  runVerification
};
